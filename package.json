{"type": "module", "version": "5.3.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build:test": "vue-tsc -b && vite build --mode test", "serve": "http-server ./dist -o", "serve:test": "http-server ./dist-test -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc -b", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "preinstall": "npx only-allow pnpm", "postinstall": "simple-git-hooks", "taze": "taze minor -wIr", "commit": "git cz", "release": "bumpp"}, "dependencies": {"@number-flow/vue": "^0.4.7", "@vee-validate/zod": "^4.15.0", "@vueuse/components": "^13.2.0", "@vueuse/core": "^13.2.0", "@vueuse/integrations": "^13.2.0", "axios": "^1.9.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "defu": "^6.1.4", "disable-devtool": "^0.3.8", "element-plus": "^2.9.10", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-fade": "^8.6.0", "embla-carousel-vue": "^8.6.0", "eruda": "^3.4.1", "es-toolkit": "^1.37.2", "hotkeys-js": "^3.13.10", "lucide-vue-next": "^0.509.0", "medium-zoom": "^1.1.0", "mitt": "^3.0.1", "motion-v": "0.11.0-beta.6", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.2", "pinyin-pro": "^3.26.0", "qs": "^6.14.0", "reka-ui": "^2.2.1", "scule": "^1.3.0", "sortablejs": "^1.15.6", "spinkit": "^2.0.1", "tailwind-merge": "^3.3.0", "ua-parser-js": "^2.0.3", "v-wave": "^3.0.2", "vconsole": "^3.15.1", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.1", "vue-sonner": "^1.3.2", "watermark-js-plus": "^1.6.2", "zod": "^3.24.4"}, "devDependencies": {"@antfu/eslint-config": "^4.13.0", "@faker-js/faker": "^9.8.0", "@iconify/json": "^2.2.338", "@iconify/vue": "^5.0.0", "@intlify/eslint-plugin-vue-i18n": "^4.0.1", "@intlify/unplugin-vue-i18n": "^6.0.8", "@stylistic/stylelint-config": "^2.0.0", "@types/canvas-confetti": "^1.9.0", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.18", "@types/sortablejs": "^1.15.8", "@unocss/eslint-plugin": "^66.1.1", "@unocss/preset-legacy-compat": "^66.1.1", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "boxen": "^8.0.1", "bumpp": "^10.1.0", "cz-git": "^1.11.1", "eslint": "^9.26.0", "esno": "^4.8.0", "fs-extra": "^11.3.0", "http-server": "^14.1.1", "inquirer": "^12.6.1", "lint-staged": "^15.5.2", "npm-run-all2": "^8.0.1", "picocolors": "^1.1.1", "plop": "^4.0.1", "postcss": "^8.5.3", "postcss-nested": "^7.0.2", "sass-embedded": "^1.88.0", "simple-git-hooks": "^2.13.0", "stylelint": "^16.19.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard-scss": "^15.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.12.0", "svgo": "^3.3.2", "taze": "^19.1.0", "typescript": "^5.8.3", "unocss": "^66.1.1", "unocss-preset-animations": "^1.2.1", "unplugin-auto-import": "^19.2.0", "unplugin-turbo-console": "^2.1.3", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.5", "vite-plugin-app-loading": "^0.3.1", "vite-plugin-archiver": "^0.1.2", "vite-plugin-banner": "^0.8.1", "vite-plugin-compression2": "^1.3.3", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-pages": "^0.33.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.6", "vite-plugin-vue-meta-layouts": "^0.5.1", "vue-tsc": "^2.2.10"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "preserveUnused": true}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}