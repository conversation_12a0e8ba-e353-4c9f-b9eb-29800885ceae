/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccountButton: typeof import('./../components/AccountButton/index.vue')['default']
    FaAnimatedBeam: typeof import('./../ui/components/FaAnimatedBeam/index.vue')['default']
    FaAnimatedCountTo: typeof import('./../ui/components/FaAnimatedCountTo/index.vue')['default']
    FaAnimatedCountToGroup: typeof import('./../ui/components/FaAnimatedCountToGroup/index.vue')['default']
    FaAuth: typeof import('./../ui/components/FaAuth/index.vue')['default']
    FaAvatar: typeof import('./../ui/components/FaAvatar/index.vue')['default']
    FaBackToTop: typeof import('./../ui/components/FaBackToTop/index.vue')['default']
    FaBadge: typeof import('./../ui/components/FaBadge/index.vue')['default']
    FaBlurReveal: typeof import('./../ui/components/FaBlurReveal/index.vue')['default']
    FaBorderBeam: typeof import('./../ui/components/FaBorderBeam/index.vue')['default']
    FaButton: typeof import('./../ui/components/FaButton/index.vue')['default']
    FaCard: typeof import('./../ui/components/FaCard/index.vue')['default']
    FaCarousel: typeof import('./../ui/components/FaCarousel/index.vue')['default']
    FaCheckbox: typeof import('./../ui/components/FaCheckbox/index.vue')['default']
    FaCheckUpdates: typeof import('./../ui/components/FaCheckUpdates/index.vue')['default']
    FaCode: typeof import('./../ui/components/FaCode/index.vue')['default']
    FaCodePreview: typeof import('./../ui/components/FaCodePreview/index.vue')['default']
    FaContextMenu: typeof import('./../ui/components/FaContextMenu/index.vue')['default']
    FaCopyright: typeof import('./../ui/components/FaCopyright/index.vue')['default']
    FaCountTo: typeof import('./../ui/components/FaCountTo/index.vue')['default']
    FaDigitalCard: typeof import('./../ui/components/FaDigitalCard/index.vue')['default']
    FaDivider: typeof import('./../ui/components/FaDivider/index.vue')['default']
    FaDrawer: typeof import('./../ui/components/FaDrawer/index.vue')['default']
    FaDropdown: typeof import('./../ui/components/FaDropdown/index.vue')['default']
    FaFixedActionBar: typeof import('./../ui/components/FaFixedActionBar/index.vue')['default']
    FaFlipCard: typeof import('./../ui/components/FaFlipCard/index.vue')['default']
    FaFlipWords: typeof import('./../ui/components/FaFlipWords/index.vue')['default']
    FaGlowyCard: typeof import('./../ui/components/FaGlowyCard/index.vue')['default']
    FaGlowyCardWrapper: typeof import('./../ui/components/FaGlowyCardWrapper/index.vue')['default']
    FaGradientButton: typeof import('./../ui/components/FaGradientButton/index.vue')['default']
    FaHoverCard: typeof import('./../ui/components/FaHoverCard/index.vue')['default']
    FaIcon: typeof import('./../ui/components/FaIcon/index.vue')['default']
    FaIconPicker: typeof import('./../ui/components/FaIconPicker/index.vue')['default']
    FaInput: typeof import('./../ui/components/FaInput/index.vue')['default']
    FaInteractiveButton: typeof import('./../ui/components/FaInteractiveButton/index.vue')['default']
    FaKbd: typeof import('./../ui/components/FaKbd/index.vue')['default']
    FaLayoutContainer: typeof import('./../ui/components/FaLayoutContainer/index.vue')['default']
    FaLinkPreview: typeof import('./../ui/components/FaLinkPreview/index.vue')['default']
    FaLoading: typeof import('./../ui/components/FaLoading/index.vue')['default']
    FaLoginAgain: typeof import('./../ui/components/FaLoginAgain/index.vue')['default']
    FaMarquee: typeof import('./../ui/components/FaMarquee/index.vue')['default']
    FaModal: typeof import('./../ui/components/FaModal/index.vue')['default']
    FaNotAllowed: typeof import('./../ui/components/FaNotAllowed/index.vue')['default']
    FaNotification: typeof import('./../ui/components/FaNotification/index.vue')['default']
    FaPageHeader: typeof import('./../ui/components/FaPageHeader/index.vue')['default']
    FaPageMain: typeof import('./../ui/components/FaPageMain/index.vue')['default']
    FaPagination: typeof import('./../ui/components/FaPagination/index.vue')['default']
    FaParticlesBg: typeof import('./../ui/components/FaParticlesBg/index.vue')['default']
    FaPasswordStrength: typeof import('./../ui/components/FaPasswordStrength/index.vue')['default']
    FaPatternBg: typeof import('./../ui/components/FaPatternBg/index.vue')['default']
    FaPinInput: typeof import('./../ui/components/FaPinInput/index.vue')['default']
    FaPopover: typeof import('./../ui/components/FaPopover/index.vue')['default']
    FaProgress: typeof import('./../ui/components/FaProgress/index.vue')['default']
    FaScratchOff: typeof import('./../ui/components/FaScratchOff/index.vue')['default']
    FaScrollArea: typeof import('./../ui/components/FaScrollArea/index.vue')['default']
    FaSearchBar: typeof import('./../ui/components/FaSearchBar/index.vue')['default']
    FaSelect: typeof import('./../ui/components/FaSelect/index.vue')['default']
    FaSlider: typeof import('./../ui/components/FaSlider/index.vue')['default']
    FaSmartFixedBlock: typeof import('./../ui/components/FaSmartFixedBlock/index.vue')['default']
    FaSparklesText: typeof import('./../ui/components/FaSparklesText/index.vue')['default']
    FaSparkline: typeof import('./../ui/components/FaSparkline/index.vue')['default']
    FaSpotlightCard: typeof import('./../ui/components/FaSpotlightCard/index.vue')['default']
    FaStorageBox: typeof import('./../ui/components/FaStorageBox/index.vue')['default']
    FaSwitch: typeof import('./../ui/components/FaSwitch/index.vue')['default']
    FaSystemInfo: typeof import('./../ui/components/FaSystemInfo/index.vue')['default']
    FaTabs: typeof import('./../ui/components/FaTabs/index.vue')['default']
    FaTextHighlight: typeof import('./../ui/components/FaTextHighlight/index.vue')['default']
    FaTimeAgo: typeof import('./../ui/components/FaTimeAgo/index.vue')['default']
    FaTimeline: typeof import('./../ui/components/FaTimeline/index.vue')['default']
    FaToast: typeof import('./../ui/components/FaToast/index.vue')['default']
    FaTooltip: typeof import('./../ui/components/FaTooltip/index.vue')['default']
    FaTrend: typeof import('./../ui/components/FaTrend/index.vue')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImagesUpload: typeof import('./../components/ImagesUpload/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    PcasCascader: typeof import('./../components/PcasCascader/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
