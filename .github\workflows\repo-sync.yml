name: repo-sync

on:
  # 每天 00:10 自动同步
  schedule:
    - cron: '10 0 * * *'
  # 手动触发部署
  workflow_dispatch:

jobs:
  sync-to-gitee:
    runs-on: ubuntu-latest
    steps:
      - name: Sync to <PERSON><PERSON><PERSON>
        uses: wearerequired/git-mirror-action@master
        env:
          # 注意在 Settings->Secrets 配置 GITEE_RSA_PRIVATE_KEY
          SSH_PRIVATE_KEY: ${{ secrets.GITEE_RSA_PRIVATE_KEY }}
        with:
          # 注意替换为你的 GitHub 源仓库地址
          source-repo: **************:fantastic-admin/pro.git
          # 注意替换为你的 Gitee 目标仓库地址
          destination-repo: *************:fantastic-admin/pro.git
