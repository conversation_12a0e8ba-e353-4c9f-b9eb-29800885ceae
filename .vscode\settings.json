{"eslint.useFlatConfig": true, "prettier.enable": false, "editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit", "source.organizeImports": "never"}, "stylelint.validate": ["css", "postcss", "scss", "vue"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "markdown", "json", "jsonc", "yaml"], "i18n-ally.localesPaths": ["src/locales/lang"], "i18n-ally.displayLanguage": "zh-cn", "i18n-ally.editor.preferEditor": true, "i18n-ally.keystyle": "nested", "i18n-ally.sortKeys": true, "i18n-ally.keepFulfilled": true, "i18n-ally.indent": 2, "typescript.tsdk": "node_modules/typescript/lib"}